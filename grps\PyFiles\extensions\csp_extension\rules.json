[{"id": 1, "priority": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "content-security-policy", "operation": "remove"}, {"header": "content-security-policy-report-only", "operation": "remove"}]}, "condition": {"urlFilter": "*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 2, "priority": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "x-frame-options", "operation": "remove"}, {"header": "x-content-type-options", "operation": "remove"}]}, "condition": {"urlFilter": "*", "resourceTypes": ["main_frame", "sub_frame"]}}]