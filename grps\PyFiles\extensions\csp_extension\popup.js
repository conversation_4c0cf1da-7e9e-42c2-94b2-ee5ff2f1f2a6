// CSP Extension Popup Script
document.addEventListener('DOMContentLoaded', function() {
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');
    const toggleBtn = document.getElementById('toggleBtn');
    
    // Get current CSP state
    function updateStatus() {
        chrome.runtime.sendMessage({ action: 'getCSPState' }, function(response) {
            if (response) {
                const isDisabled = response.disabled;
                
                if (isDisabled) {
                    statusIndicator.textContent = '🚫';
                    statusText.textContent = 'CSP Disabled';
                    statusText.className = 'status-text disabled';
                    toggleBtn.textContent = 'Enable CSP';
                } else {
                    statusIndicator.textContent = '🛡️';
                    statusText.textContent = 'CSP Enabled';
                    statusText.className = 'status-text enabled';
                    toggleBtn.textContent = 'Disable CSP';
                }
            }
        });
    }
    
    // Toggle CSP state
    toggleBtn.addEventListener('click', function() {
        toggleBtn.disabled = true;
        toggleBtn.textContent = 'Updating...';
        
        chrome.runtime.sendMessage({ action: 'toggleCSP' }, function(response) {
            if (response && response.success) {
                updateStatus();
            } else {
                console.error('Failed to toggle CSP:', response ? response.error : 'Unknown error');
                statusText.textContent = 'Error updating CSP';
            }
            
            toggleBtn.disabled = false;
        });
    });
    
    // Initial status update
    updateStatus();
});
