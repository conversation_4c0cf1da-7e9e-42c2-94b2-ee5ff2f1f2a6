"""
Enhanced Proxy Manager for Phase 4.1 Implementation
Provides seamless proxy rotation, health checking, geographic IP matching, and sticky sessions
"""

import json
import time
import random
import logging
import requests
import threading
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import os

# Suppress SSL warnings for proxy health checks
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

@dataclass
class ProxyInfo:
    """Data class for proxy information"""
    host: str
    port: int
    username: str = None
    password: str = None
    protocol: str = "http"
    country: str = None
    timezone: str = None
    last_health_check: datetime = None
    health_status: str = "unknown"  # unknown, healthy, unhealthy
    response_time: float = None
    failure_count: int = 0
    success_count: int = 0
    
    def to_url(self) -> str:
        """Convert proxy info to URL format"""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        return f"{self.protocol}://{self.host}:{self.port}"
    
    def get_health_score(self) -> float:
        """Calculate health score based on success/failure ratio and response time"""
        if self.success_count + self.failure_count == 0:
            return 0.5  # Neutral score for untested proxies
        
        success_ratio = self.success_count / (self.success_count + self.failure_count)
        
        # Factor in response time (lower is better)
        time_factor = 1.0
        if self.response_time:
            # Penalize slow proxies (>5 seconds)
            time_factor = max(0.1, 1.0 - (self.response_time - 1.0) / 10.0)
        
        return success_ratio * time_factor

class EnhancedProxyManager:
    """Enhanced proxy manager with health checking, rotation, and geographic matching"""
    
    def __init__(self, config_path: str = None):
        """Initialize the enhanced proxy manager"""
        self.logger = logging.getLogger("EnhancedProxyManager")
        
        # Configuration paths
        self.config_path = config_path or os.path.join(os.path.dirname(__file__), "json", "enhanced_settings.json")
        self.proxy_pool_path = os.path.join(os.path.dirname(__file__), "json", "proxy_pool.json")
        self.session_cache_path = os.path.join(os.path.dirname(__file__), "json", "proxy_sessions.json")
        
        # Load configuration
        self.config = self._load_config()
        
        # Proxy management
        self.proxy_pool: List[ProxyInfo] = []
        self.session_proxies: Dict[str, str] = {}  # email -> proxy_url mapping
        self.health_check_thread = None
        self.health_check_running = False
        
        # Geographic data cache
        self.geo_cache: Dict[str, Dict] = {}
        
        # Initialize proxy pool and sessions
        self._load_proxy_pool()
        self._load_session_cache()
        
        # Start health checking if enabled
        if self.config.get("proxy", {}).get("health_check_enabled", True):
            self._start_health_checking()
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    return json.load(f)
            else:
                # Create default configuration
                default_config = {
                    "proxy": {
                        "enabled": True,
                        "health_check_enabled": True,
                        "health_check_interval": 300,  # 5 minutes
                        "connection_timeout": 30,
                        "read_timeout": 60,
                        "max_retries": 3,
                        "retry_delay": 5,
                        "fallback_on_failure": True,
                        "geographic_matching": True,
                        "sticky_sessions": True
                    }
                }
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                with open(self.config_path, 'w') as f:
                    json.dump(default_config, f, indent=4)
                return default_config
        except Exception as e:
            self.logger.error(f"Error loading config: {e}")
            return {}
    
    def _load_proxy_pool(self):
        """Load proxy pool from configuration and external sources"""
        try:
            # Load from proxy pool file if exists
            if os.path.exists(self.proxy_pool_path):
                with open(self.proxy_pool_path, 'r') as f:
                    proxy_data = json.load(f)
                    # Convert datetime strings back to datetime objects
                    for proxy in proxy_data:
                        if proxy.get('last_health_check') and isinstance(proxy['last_health_check'], str):
                            try:
                                proxy['last_health_check'] = datetime.fromisoformat(proxy['last_health_check'])
                            except:
                                proxy['last_health_check'] = None
                    self.proxy_pool = [ProxyInfo(**proxy) for proxy in proxy_data]
            
            # Add proxy from main config if not in pool
            main_proxy = self.config.get("proxy", {})
            if main_proxy.get("enabled") and main_proxy.get("host"):
                main_proxy_info = ProxyInfo(
                    host=main_proxy["host"],
                    port=main_proxy["port"],
                    username=main_proxy.get("username"),
                    password=main_proxy.get("password"),
                    protocol=main_proxy.get("protocol", "http")
                )
                
                # Check if this proxy is already in pool
                if not any(p.host == main_proxy_info.host and p.port == main_proxy_info.port 
                          for p in self.proxy_pool):
                    self.proxy_pool.append(main_proxy_info)
            
            self.logger.info(f"Loaded {len(self.proxy_pool)} proxies into pool")
            
        except Exception as e:
            self.logger.error(f"Error loading proxy pool: {e}")
    
    def _load_session_cache(self):
        """Load session-proxy mappings from cache"""
        try:
            if os.path.exists(self.session_cache_path):
                with open(self.session_cache_path, 'r') as f:
                    self.session_proxies = json.load(f)
                self.logger.info(f"Loaded {len(self.session_proxies)} session mappings")
        except Exception as e:
            self.logger.error(f"Error loading session cache: {e}")
    
    def _save_session_cache(self):
        """Save session-proxy mappings to cache"""
        try:
            os.makedirs(os.path.dirname(self.session_cache_path), exist_ok=True)
            with open(self.session_cache_path, 'w') as f:
                json.dump(self.session_proxies, f, indent=4)
        except Exception as e:
            self.logger.error(f"Error saving session cache: {e}")
    
    def _save_proxy_pool(self):
        """Save proxy pool to file"""
        try:
            os.makedirs(os.path.dirname(self.proxy_pool_path), exist_ok=True)
            proxy_data = [asdict(proxy) for proxy in self.proxy_pool]
            # Convert datetime objects to strings
            for proxy in proxy_data:
                if proxy.get('last_health_check'):
                    if isinstance(proxy['last_health_check'], datetime):
                        proxy['last_health_check'] = proxy['last_health_check'].isoformat()
                    # If it's already a string, leave it as is

            with open(self.proxy_pool_path, 'w') as f:
                json.dump(proxy_data, f, indent=4)
        except Exception as e:
            self.logger.error(f"Error saving proxy pool: {e}")
    
    def add_proxy(self, host: str, port: int, username: str = None, password: str = None, 
                  protocol: str = "http", country: str = None, timezone: str = None):
        """Add a new proxy to the pool"""
        proxy_info = ProxyInfo(
            host=host,
            port=port,
            username=username,
            password=password,
            protocol=protocol,
            country=country,
            timezone=timezone
        )
        
        # Check if proxy already exists
        if not any(p.host == host and p.port == port for p in self.proxy_pool):
            self.proxy_pool.append(proxy_info)
            self._save_proxy_pool()
            self.logger.info(f"Added proxy {host}:{port} to pool")
        else:
            self.logger.warning(f"Proxy {host}:{port} already exists in pool")
    
    def get_proxy_for_session(self, session_id: str, preferred_country: str = None, 
                            preferred_timezone: str = None) -> Optional[str]:
        """Get a proxy for a specific session with sticky session support"""
        try:
            # Check if session already has a proxy (sticky session)
            if (self.config.get("proxy", {}).get("sticky_sessions", True) and 
                session_id in self.session_proxies):
                
                existing_proxy_url = self.session_proxies[session_id]
                
                # Verify the proxy is still healthy
                if self._is_proxy_healthy(existing_proxy_url):
                    self.logger.info(f"Using existing proxy for session {session_id}")
                    return existing_proxy_url
                else:
                    self.logger.warning(f"Existing proxy for session {session_id} is unhealthy, selecting new one")
                    del self.session_proxies[session_id]
            
            # Select best proxy based on criteria
            best_proxy = self._select_best_proxy(preferred_country, preferred_timezone)
            
            if best_proxy:
                proxy_url = best_proxy.to_url()
                self.session_proxies[session_id] = proxy_url
                self._save_session_cache()
                self.logger.info(f"Assigned proxy {best_proxy.host}:{best_proxy.port} to session {session_id}")
                return proxy_url
            
            self.logger.warning("No healthy proxy available")
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting proxy for session: {e}")
            return None

    def _select_best_proxy(self, preferred_country: str = None,
                          preferred_timezone: str = None) -> Optional[ProxyInfo]:
        """Select the best proxy based on health score and geographic preferences"""
        if not self.proxy_pool:
            return None

        # Filter healthy proxies
        healthy_proxies = [p for p in self.proxy_pool if p.health_status != "unhealthy"]

        if not healthy_proxies:
            self.logger.warning("No healthy proxies available, using any available proxy")
            healthy_proxies = self.proxy_pool

        # Apply geographic filtering if enabled and preferences provided
        if (self.config.get("proxy", {}).get("geographic_matching", True) and
            (preferred_country or preferred_timezone)):

            geo_filtered = []
            for proxy in healthy_proxies:
                if preferred_country and proxy.country == preferred_country:
                    geo_filtered.append(proxy)
                elif preferred_timezone and proxy.timezone == preferred_timezone:
                    geo_filtered.append(proxy)

            if geo_filtered:
                healthy_proxies = geo_filtered
                self.logger.info(f"Filtered to {len(geo_filtered)} geographically matching proxies")

        # Sort by health score (highest first)
        healthy_proxies.sort(key=lambda p: p.get_health_score(), reverse=True)

        # Add some randomization to avoid always using the same "best" proxy
        top_proxies = healthy_proxies[:min(3, len(healthy_proxies))]
        return random.choice(top_proxies)

    def _is_proxy_healthy(self, proxy_url: str) -> bool:
        """Check if a specific proxy URL is healthy"""
        for proxy in self.proxy_pool:
            if proxy.to_url() == proxy_url:
                return proxy.health_status == "healthy"
        return False

    def _start_health_checking(self):
        """Start the health checking thread"""
        if self.health_check_thread and self.health_check_thread.is_alive():
            return

        self.health_check_running = True
        self.health_check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self.health_check_thread.start()
        self.logger.info("Started proxy health checking thread")

    def _stop_health_checking(self):
        """Stop the health checking thread"""
        self.health_check_running = False
        if self.health_check_thread:
            self.health_check_thread.join(timeout=5)

    def pause_health_checking(self):
        """Pause health checking temporarily (for critical operations)"""
        if not hasattr(self, '_health_check_paused'):
            self._health_check_paused = False
        self._health_check_paused = True
        self.logger.debug("Proxy health checking paused for critical operation")

    def resume_health_checking(self):
        """Resume health checking after critical operations"""
        if not hasattr(self, '_health_check_paused'):
            self._health_check_paused = False
        self._health_check_paused = False
        self.logger.debug("Proxy health checking resumed")

    def pause_health_checking(self):
        """Pause health checking temporarily (for critical operations)"""
        if hasattr(self, '_health_check_paused'):
            self._health_check_paused = True
        else:
            self._health_check_paused = True
        self.logger.debug("Proxy health checking paused for critical operation")

    def resume_health_checking(self):
        """Resume health checking after critical operations"""
        if hasattr(self, '_health_check_paused'):
            self._health_check_paused = False
        else:
            self._health_check_paused = False
        self.logger.debug("Proxy health checking resumed")

    def _health_check_loop(self):
        """Main health checking loop"""
        interval = self.config.get("proxy", {}).get("health_check_interval", 300)
        self._health_check_paused = False  # Initialize pause flag

        while self.health_check_running:
            try:
                # Check if health checking is paused
                if not getattr(self, '_health_check_paused', False):
                    self.logger.info("Starting proxy health check cycle")
                    self._check_all_proxies_health()
                    self._save_proxy_pool()
                else:
                    self.logger.debug("Proxy health check cycle skipped (paused for critical operation)")

                # Wait for next cycle
                for _ in range(interval):
                    if not self.health_check_running:
                        break
                    time.sleep(1)

            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
                time.sleep(30)  # Wait before retrying

    def _check_all_proxies_health(self):
        """Check health of all proxies in the pool"""
        for proxy in self.proxy_pool:
            try:
                self._check_proxy_health(proxy)
            except Exception as e:
                self.logger.error(f"Error checking proxy {proxy.host}:{proxy.port}: {e}")
                proxy.health_status = "unhealthy"
                proxy.failure_count += 1

    def _check_proxy_health(self, proxy: ProxyInfo):
        """Check health of a single proxy"""
        test_urls = [
            "http://httpbin.org/ip",
            "https://api.ipify.org?format=json",
            "http://ip-api.com/json"
        ]

        proxy_dict = {
            'http': proxy.to_url(),
            'https': proxy.to_url()
        }

        timeout = self.config.get("proxy", {}).get("connection_timeout", 30)

        for test_url in test_urls:
            try:
                start_time = time.time()
                # Use verify=False for proxy health checks as proxies may use self-signed certificates
                # SSL warnings are suppressed at module level to avoid noise in logs
                response = requests.get(
                    test_url,
                    proxies=proxy_dict,
                    timeout=timeout,
                    verify=False
                )

                if response.status_code == 200:
                    response_time = time.time() - start_time
                    proxy.response_time = response_time
                    proxy.health_status = "healthy"
                    proxy.success_count += 1
                    proxy.last_health_check = datetime.now()

                    # Try to get geographic information
                    if not proxy.country or not proxy.timezone:
                        self._update_proxy_geo_info(proxy, response.text)

                    self.logger.debug(f"Proxy {proxy.host}:{proxy.port} is healthy (response time: {response_time:.2f}s)")
                    return

            except Exception as e:
                self.logger.debug(f"Health check failed for {proxy.host}:{proxy.port} with {test_url}: {e}")
                continue

        # If we get here, all tests failed
        proxy.health_status = "unhealthy"
        proxy.failure_count += 1
        proxy.last_health_check = datetime.now()
        self.logger.warning(f"Proxy {proxy.host}:{proxy.port} marked as unhealthy")

    def _update_proxy_geo_info(self, proxy: ProxyInfo, response_text: str):
        """Update proxy geographic information from IP geolocation"""
        try:
            # Try to extract IP from response
            ip = None
            if "ip" in response_text.lower():
                import re
                ip_match = re.search(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', response_text)
                if ip_match:
                    ip = ip_match.group()

            if ip and ip not in self.geo_cache:
                # Get geographic information
                geo_response = requests.get(
                    f"http://ip-api.com/json/{ip}",
                    timeout=10
                )

                if geo_response.status_code == 200:
                    geo_data = geo_response.json()
                    if geo_data.get("status") == "success":
                        self.geo_cache[ip] = geo_data
                        proxy.country = geo_data.get("countryCode")
                        proxy.timezone = geo_data.get("timezone")
                        self.logger.info(f"Updated geo info for proxy {proxy.host}:{proxy.port}: {proxy.country}, {proxy.timezone}")

        except Exception as e:
            self.logger.debug(f"Error updating geo info for proxy: {e}")

    def get_proxy_url(self) -> Optional[str]:
        """Get a proxy URL (for backward compatibility)"""
        if not self.proxy_pool:
            return None

        # Use the first healthy proxy for backward compatibility
        healthy_proxies = [p for p in self.proxy_pool if p.health_status == "healthy"]
        if healthy_proxies:
            return healthy_proxies[0].to_url()

        # Fallback to any proxy if no healthy ones
        return self.proxy_pool[0].to_url() if self.proxy_pool else None

    def should_use_proxy(self) -> bool:
        """Check if proxy should be used"""
        return (self.config.get("proxy", {}).get("enabled", False) and
                len(self.proxy_pool) > 0)

    def get_proxy_stats(self) -> Dict:
        """Get proxy pool statistics"""
        if not self.proxy_pool:
            return {"total": 0, "healthy": 0, "unhealthy": 0, "unknown": 0}

        stats = {"total": len(self.proxy_pool), "healthy": 0, "unhealthy": 0, "unknown": 0}

        for proxy in self.proxy_pool:
            stats[proxy.health_status] += 1

        return stats

    def remove_proxy(self, host: str, port: int):
        """Remove a proxy from the pool"""
        self.proxy_pool = [p for p in self.proxy_pool if not (p.host == host and p.port == port)]
        self._save_proxy_pool()
        self.logger.info(f"Removed proxy {host}:{port} from pool")

    def clear_session_cache(self):
        """Clear all session-proxy mappings"""
        self.session_proxies.clear()
        self._save_session_cache()
        self.logger.info("Cleared session proxy cache")

    def __del__(self):
        """Cleanup when object is destroyed"""
        self._stop_health_checking()
