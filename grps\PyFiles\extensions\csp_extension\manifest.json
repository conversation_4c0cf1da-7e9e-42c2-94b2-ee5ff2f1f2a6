{"manifest_version": 3, "name": "CSP Disabler", "version": "1.0.0", "description": "Disable Content Security Policy for automation and testing", "permissions": ["storage", "activeTab", "declarativeNetRequest", "declarativeNetRequestWithHostAccess"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js", "type": "module"}, "action": {"default_popup": "popup.html", "default_title": "CSP Disabler"}, "declarative_net_request": {"rule_resources": [{"id": "csp_rules", "enabled": true, "path": "rules.json"}]}}