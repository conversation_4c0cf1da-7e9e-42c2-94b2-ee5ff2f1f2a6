"""
Chrome Proxy Extension Manager for SeleniumBase Integration
Handles proxy extension creation, configuration, and management for proxy functionality only
"""

import os
import json
import zipfile
import tempfile
import logging
from typing import Dict, Optional, List
from pathlib import Path


class ProxyExtensionManager:
    """Manages Chrome proxy extensions for SeleniumBase integration - proxy functionality only"""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.extensions_dir = os.path.join(os.path.dirname(__file__), 'extensions')
        os.makedirs(self.extensions_dir, exist_ok=True)
        self.shared_extension_name = "shared_proxy_extension"
        self.shared_extension_path = os.path.join(self.extensions_dir, self.shared_extension_name)

    def get_or_create_shared_extension(self) -> str:
        """Get or create the shared proxy extension for dynamic proxy configuration"""
        try:
            # Check if shared extension already exists
            if os.path.exists(self.shared_extension_path):
                self.logger.info(f"Using existing shared proxy extension: {self.shared_extension_path}")
                return self.shared_extension_path

            # Create shared extension
            os.makedirs(self.shared_extension_path, exist_ok=True)

            # Create manifest.json for shared proxy extension
            manifest = self._create_shared_manifest()
            with open(os.path.join(self.shared_extension_path, 'manifest.json'), 'w') as f:
                json.dump(manifest, f, indent=2)

            # Create simplified proxy background script
            background_js = self._create_simple_background_script()
            with open(os.path.join(self.shared_extension_path, 'background.js'), 'w') as f:
                f.write(background_js)

            # Create content script for proxy configuration injection
            content_js = self._create_content_script()
            with open(os.path.join(self.shared_extension_path, 'content.js'), 'w') as f:
                f.write(content_js)

            # Create popup HTML (for proxy debugging and testing)
            popup_html = self._create_popup_html()
            with open(os.path.join(self.shared_extension_path, 'popup.html'), 'w') as f:
                f.write(popup_html)

            # Create popup script for proxy management
            popup_js = self._create_popup_script()
            with open(os.path.join(self.shared_extension_path, 'popup.js'), 'w') as f:
                f.write(popup_js)

            self.logger.info(f"Created shared proxy extension at: {self.shared_extension_path}")
            return self.shared_extension_path

        except Exception as e:
            self.logger.error(f"Error creating shared proxy extension: {str(e)}")
            raise

    def create_proxy_extension(self, proxy_config: Dict, extension_name: str = "proxy_extension") -> str:
        """
        Create a Chrome proxy extension with the given proxy configuration
        
        Args:
            proxy_config: Dictionary containing proxy settings
            extension_name: Name for the extension
            
        Returns:
            Path to the created extension directory
        """
        try:
            extension_path = os.path.join(self.extensions_dir, extension_name)
            os.makedirs(extension_path, exist_ok=True)
            
            # Create manifest.json
            manifest = self._create_manifest()
            with open(os.path.join(extension_path, 'manifest.json'), 'w') as f:
                json.dump(manifest, f, indent=2)
            
            # Create simplified background script
            background_js = self._create_simple_background_script()
            with open(os.path.join(extension_path, 'background.js'), 'w') as f:
                f.write(background_js)
            
            # Create popup HTML (optional, for debugging)
            popup_html = self._create_popup_html()
            with open(os.path.join(extension_path, 'popup.html'), 'w') as f:
                f.write(popup_html)
            
            # Create popup script
            popup_js = self._create_popup_script()
            with open(os.path.join(extension_path, 'popup.js'), 'w') as f:
                f.write(popup_js)
            
            self.logger.info(f"Created proxy extension at: {extension_path}")
            return extension_path
            
        except Exception as e:
            self.logger.error(f"Error creating proxy extension: {str(e)}")
            raise
    
    def create_proxy_extension_zip(self, proxy_config: Dict, extension_name: str = "proxy_extension") -> str:
        """
        Create a zipped Chrome proxy extension
        
        Args:
            proxy_config: Dictionary containing proxy settings
            extension_name: Name for the extension
            
        Returns:
            Path to the created extension zip file
        """
        try:
            # Create temporary directory for extension files
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create extension files in temp directory
                manifest = self._create_manifest()
                with open(os.path.join(temp_dir, 'manifest.json'), 'w') as f:
                    json.dump(manifest, f, indent=2)
                
                background_js = self._create_simple_background_script()
                with open(os.path.join(temp_dir, 'background.js'), 'w') as f:
                    f.write(background_js)
                
                popup_html = self._create_popup_html()
                with open(os.path.join(temp_dir, 'popup.html'), 'w') as f:
                    f.write(popup_html)
                
                popup_js = self._create_popup_script()
                with open(os.path.join(temp_dir, 'popup.js'), 'w') as f:
                    f.write(popup_js)
                
                # Create zip file
                zip_path = os.path.join(self.extensions_dir, f"{extension_name}.zip")
                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, temp_dir)
                            zipf.write(file_path, arcname)
                
                self.logger.info(f"Created proxy extension zip at: {zip_path}")
                return zip_path
                
        except Exception as e:
            self.logger.error(f"Error creating proxy extension zip: {str(e)}")
            raise
    
    def _create_shared_manifest(self) -> Dict:
        """Create manifest.json for the shared proxy extension"""
        return {
            "manifest_version": 3,
            "name": "Simple HTTP Proxy",
            "version": "1.0",
            "description": "A simple HTTP proxy with authentication for Chrome",
            "permissions": [
                "proxy",
                "storage",
                "activeTab",
                "webRequest",
                "webRequestAuthProvider"
            ],
            "host_permissions": [
                "<all_urls>"
            ],
            "background": {
                "service_worker": "background.js",
                "type": "module"
            }
        }

    def _create_manifest(self) -> Dict:
        """Create manifest.json for the proxy extension"""
        return {
            "manifest_version": 3,
            "name": "Simple HTTP Proxy",
            "version": "1.0",
            "description": "A simple HTTP proxy with authentication for Chrome",
            "permissions": [
                "proxy",
                "storage",
                "activeTab",
                "webRequest",
                "webRequestAuthProvider"
            ],
            "host_permissions": [
                "<all_urls>"
            ],
            "background": {
                "service_worker": "background.js",
                "type": "module"
            }
        }

    def _create_shared_background_script(self) -> str:
        """Create background.js for the shared proxy extension with dynamic configuration"""
        return '''
// Chrome Proxy Extension Background Script
console.log('Proxy extension background script loaded');

// Global proxy configuration storage
let currentProxyConfig = null;
let isProxyConfigured = false;

// Initialize extension
chrome.runtime.onStartup.addListener(() => {
    console.log('Extension startup - loading proxy configuration');
    loadProxyConfiguration();
});

chrome.runtime.onInstalled.addListener(() => {
    console.log('Extension installed - loading proxy configuration');
    loadProxyConfiguration();
});

// Load proxy configuration from storage or external source
async function loadProxyConfiguration() {
    try {
        // First try to get configuration from Chrome storage
        const result = await chrome.storage.local.get(['proxyConfig', 'proxyUsername', 'proxyPassword', 'proxyHost', 'proxyPort']);

        if (result.proxyConfig) {
            console.log('Loaded proxy configuration from storage');
            currentProxyConfig = result.proxyConfig;
            await configureProxy(currentProxyConfig);
        } else if (result.proxyHost && result.proxyUsername && result.proxyPassword) {
            // Reconstruct config from individual stored values
            console.log('Reconstructing proxy configuration from stored values');
            const reconstructedConfig = {
                host: result.proxyHost,
                port: result.proxyPort || 8080,
                username: result.proxyUsername,
                password: result.proxyPassword,
                protocol: 'http'
            };
            currentProxyConfig = reconstructedConfig;
            await configureProxy(currentProxyConfig);
        } else {
            console.log('No proxy configuration found in storage, waiting for injection');
            // Configuration will be injected by the automation script
        }
    } catch (error) {
        console.error('Error loading proxy configuration:', error);
    }
}

// Configure proxy with the given settings
async function configureProxy(proxyConfig) {
    if (!proxyConfig || !proxyConfig.host) {
        console.log('No valid proxy configuration provided');
        return false;
    }

    try {
        // Configure proxy for both HTTP and HTTPS traffic
        const proxyServer = {
            scheme: proxyConfig.protocol || 'http',
            host: proxyConfig.host,
            port: proxyConfig.port || 8080
        };

        const config = {
            mode: "fixed_servers",
            rules: {
                // Use the same proxy for both HTTP and HTTPS
                httpProxy: proxyServer,
                httpsProxy: proxyServer,
                // Fallback for other protocols
                singleProxy: proxyServer,
                bypassList: ["localhost", "127.0.0.1", "<local>"]
            }
        };

        await new Promise((resolve, reject) => {
            chrome.proxy.settings.set({
                value: config,
                scope: 'regular'
            }, () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        });

        console.log('Proxy configured successfully:', proxyConfig.host + ':' + proxyConfig.port);
        console.log('Proxy configuration details:', JSON.stringify(config, null, 2));

        // Store current configuration and status
        currentProxyConfig = proxyConfig;
        isProxyConfigured = true;

        // Store complete proxy configuration for authentication
        await chrome.storage.local.set({
            proxyStatus: 'active',
            proxyHost: proxyConfig.host,
            proxyPort: proxyConfig.port,
            proxyProtocol: proxyConfig.protocol || 'http',
            proxyUsername: proxyConfig.username || '',
            proxyPassword: proxyConfig.password || '',
            proxyConfig: proxyConfig,  // Store complete config for auth handler
            lastConfigured: new Date().toISOString(),
            configDetails: JSON.stringify(config)
        });

        // Test proxy connectivity
        console.log('Testing proxy connectivity...');
        try {
            // Make a test request to verify proxy is working
            const testResponse = await fetch('https://httpbin.org/ip', {
                method: 'GET',
                cache: 'no-cache'
            });

            if (testResponse.ok) {
                const ipData = await testResponse.json();
                console.log('Proxy test successful. Current IP:', ipData.origin);

                await chrome.storage.local.set({
                    proxyTestStatus: 'success',
                    proxyTestIP: ipData.origin,
                    lastProxyTest: new Date().toISOString()
                });
            } else {
                console.warn('Proxy test request failed with status:', testResponse.status);
            }
        } catch (testError) {
            console.warn('Proxy connectivity test failed:', testError);
            await chrome.storage.local.set({
                proxyTestStatus: 'failed',
                proxyTestError: testError.message,
                lastProxyTest: new Date().toISOString()
            });
        }

        return true;

    } catch (error) {
        console.error('Error configuring proxy:', error);
        await chrome.storage.local.set({
            proxyStatus: 'error',
            lastError: error.message,
            errorTime: new Date().toISOString()
        });
        return false;
    }
}

// Handle proxy authentication - this is the key to eliminating popups
chrome.webRequest.onAuthRequired.addListener(
    async (details) => {
        console.log('Proxy authentication required for:', details.url);
        console.log('Auth details:', JSON.stringify(details, null, 2));

        // First try to use current config
        if (currentProxyConfig && currentProxyConfig.username && currentProxyConfig.password) {
            console.log('Providing proxy authentication credentials from current config');
            return {
                authCredentials: {
                    username: currentProxyConfig.username,
                    password: currentProxyConfig.password
                }
            };
        }

        // Fallback: try to get credentials from storage
        try {
            const result = await chrome.storage.local.get(['proxyConfig', 'proxyUsername', 'proxyPassword']);

            if (result.proxyConfig && result.proxyConfig.username && result.proxyConfig.password) {
                console.log('Providing proxy authentication credentials from stored config');
                // Update current config if it was missing
                currentProxyConfig = result.proxyConfig;
                return {
                    authCredentials: {
                        username: result.proxyConfig.username,
                        password: result.proxyConfig.password
                    }
                };
            } else if (result.proxyUsername && result.proxyPassword) {
                console.log('Providing proxy authentication credentials from stored credentials');
                return {
                    authCredentials: {
                        username: result.proxyUsername,
                        password: result.proxyPassword
                    }
                };
            }
        } catch (error) {
            console.error('Error retrieving proxy credentials from storage:', error);
        }

        console.warn('No proxy credentials available for authentication - this will show popup');
        console.warn('Current config:', currentProxyConfig);

        // Log storage state for debugging
        chrome.storage.local.get(null, (allData) => {
            console.warn('All storage data:', allData);
        });

        return {};
    },
    {urls: ["<all_urls>"]},
    ["blocking"]
);

// Handle proxy errors
chrome.proxy.onProxyError.addListener(async (details) => {
    console.error('Proxy error:', details);
    await chrome.storage.local.set({
        proxyStatus: 'error',
        lastError: details.error,
        errorTime: new Date().toISOString()
    });
});

// Message handling for external configuration and status queries
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'configureProxy') {
        console.log('Received proxy configuration request');
        configureProxy(request.proxyConfig).then(success => {
            sendResponse({success: success});
        }).catch(error => {
            console.error('Error in configureProxy:', error);
            sendResponse({success: false, error: error.message});
        });
        return true; // Indicates we will send a response asynchronously
    }

    if (request.action === 'getProxyStatus') {
        chrome.storage.local.get(['proxyStatus', 'proxyHost', 'proxyPort', 'lastConfigured', 'lastError'], (result) => {
            sendResponse({
                ...result,
                isConfigured: isProxyConfigured,
                currentConfig: currentProxyConfig ? {
                    host: currentProxyConfig.host,
                    port: currentProxyConfig.port,
                    protocol: currentProxyConfig.protocol
                } : null
            });
        });
        return true;
    }

    if (request.action === 'reconfigureProxy') {
        if (currentProxyConfig) {
            configureProxy(currentProxyConfig).then(success => {
                sendResponse({success: success});
            });
        } else {
            sendResponse({success: false, error: 'No proxy configuration available'});
        }
        return true;
    }
});

// External API for configuration injection
chrome.runtime.onMessageExternal.addListener((request, sender, sendResponse) => {
    if (request.action === 'setProxyConfig') {
        console.log('Received external proxy configuration');
        configureProxy(request.proxyConfig).then(success => {
            sendResponse({success: success});
        });
        return true;
    }
});

// Proxy extension badge to show status
function updateExtensionIcon() {
    const badgeText = isProxyConfigured ? 'ON' : 'OFF';
    const badgeColor = isProxyConfigured ? '#00FF00' : '#FF0000';

    chrome.action.setBadgeText({ text: badgeText });
    chrome.action.setBadgeBackgroundColor({ color: badgeColor });

    console.log(`Proxy configured: ${isProxyConfigured}, badge: ${badgeText}`);
}

// Initialize proxy functionality on script load
loadProxyConfiguration();
'''.strip()

    def _create_simple_background_script(self) -> str:
        """Create simplified background.js for the shared proxy extension"""
        return '''
// Background script for proxy management
// CONFIGURE YOUR PROXY SETTINGS HERE
const PROXY_CONFIG = {
  scheme: "http",        // http, https, socks4, or socks5
  host: "gw.dataimpulse.com",    // Your proxy host
  port: 823,           // Your proxy port
  username: "e98f5489956302bda457__cr.us",    // Your proxy username
  password: "917b45d9ec594d54"     // Your proxy password
};

let proxyConfig = PROXY_CONFIG;
let isProxyEnabled = true;

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Proxy extension installed, enabling proxy...');
  setProxy(proxyConfig);
});

chrome.runtime.onStartup.addListener(() => {
  console.log('Browser started, enabling proxy...');
  setProxy(proxyConfig);
});

// Set proxy configuration
async function setProxy(config) {
  try {
    const proxySettings = {
      mode: "fixed_servers",
      rules: {
        singleProxy: {
          scheme: config.scheme || "http",
          host: config.host,
          port: parseInt(config.port)
        }
      }
    };

    await chrome.proxy.settings.set({
      value: proxySettings,
      scope: 'regular'
    });

    console.log('Proxy set successfully:', proxySettings);
  } catch (error) {
    console.error('Error setting proxy:', error);
    throw error;
  }
}

// Clear proxy settings
async function clearProxy() {
  try {
    await chrome.proxy.settings.clear({
      scope: 'regular'
    });
    console.log('Proxy cleared successfully');
  } catch (error) {
    console.error('Error clearing proxy:', error);
    throw error;
  }
}

// Handle proxy authentication
chrome.webRequest.onAuthRequired.addListener(
  (details) => {
    console.log('Proxy authentication required, providing credentials...');
    return {
      authCredentials: {
        username: proxyConfig.username,
        password: proxyConfig.password
      }
    };
  },
  { urls: ["<all_urls>"] },
  ["blocking"]
);
'''.strip()

    def _create_content_script(self) -> str:
        """Create content.js for proxy configuration injection"""
        return '''
// Content script for proxy configuration injection
console.log('Proxy extension content script loaded');

// Listen for proxy configuration from the automation script
window.addEventListener('message', function(event) {
    // Only accept messages from the same origin
    if (event.source !== window) return;

    if (event.data.type === 'PROXY_CONFIG') {
        console.log('Received proxy configuration in content script');

        // Forward the configuration to the background script
        chrome.runtime.sendMessage({
            action: 'configureProxy',
            proxyConfig: event.data.config
        }, function(response) {
            if (response && response.success) {
                console.log('Proxy configuration applied successfully');
                // Notify the page that configuration was successful
                window.postMessage({
                    type: 'PROXY_CONFIG_RESPONSE',
                    success: true
                }, '*');
            } else {
                console.error('Failed to apply proxy configuration:', response);
                window.postMessage({
                    type: 'PROXY_CONFIG_RESPONSE',
                    success: false,
                    error: response ? response.error : 'Unknown error'
                }, '*');
            }
        });
    }
});

// Expose a global function for direct configuration
window.configureProxy = function(proxyConfig) {
    return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
            action: 'configureProxy',
            proxyConfig: proxyConfig
        }, function(response) {
            if (response && response.success) {
                resolve(response);
            } else {
                reject(new Error(response ? response.error : 'Configuration failed'));
            }
        });
    });
};

// Signal that the content script is ready
window.postMessage({
    type: 'PROXY_EXTENSION_READY'
}, '*');
'''.strip()
    
    def _create_background_script(self, proxy_config: Dict) -> str:
        """Create background.js for the proxy extension"""
        
        # Extract proxy details
        proxy_host = proxy_config.get('host', '')
        proxy_port = proxy_config.get('port', 8080)
        proxy_username = proxy_config.get('username', '')
        proxy_password = proxy_config.get('password', '')
        proxy_protocol = proxy_config.get('protocol', 'http')
        
        return f'''
// Chrome Proxy Extension Background Script
console.log('Proxy extension background script loaded');

// Proxy configuration
const PROXY_CONFIG = {{
    host: '{proxy_host}',
    port: {proxy_port},
    username: '{proxy_username}',
    password: '{proxy_password}',
    protocol: '{proxy_protocol}'
}};

// Set up proxy configuration
chrome.runtime.onStartup.addListener(() => {{
    console.log('Extension startup - configuring proxy');
    configureProxy();
}});

chrome.runtime.onInstalled.addListener(() => {{
    console.log('Extension installed - configuring proxy');
    configureProxy();
}});

function configureProxy() {{
    if (!PROXY_CONFIG.host) {{
        console.log('No proxy host configured');
        return;
    }}
    
    const config = {{
        mode: "fixed_servers",
        rules: {{
            singleProxy: {{
                scheme: PROXY_CONFIG.protocol,
                host: PROXY_CONFIG.host,
                port: PROXY_CONFIG.port
            }},
            bypassList: ["localhost", "127.0.0.1", "<local>"]
        }}
    }};
    
    chrome.proxy.settings.set({{
        value: config,
        scope: 'regular'
    }}, () => {{
        if (chrome.runtime.lastError) {{
            console.error('Error setting proxy:', chrome.runtime.lastError);
        }} else {{
            console.log('Proxy configured successfully:', PROXY_CONFIG.host + ':' + PROXY_CONFIG.port);
            // Store proxy status
            chrome.storage.local.set({{
                proxyStatus: 'active',
                proxyHost: PROXY_CONFIG.host,
                proxyPort: PROXY_CONFIG.port,
                lastConfigured: new Date().toISOString()
            }});
        }}
    }});
}}

// Handle proxy authentication
chrome.webRequest.onAuthRequired.addListener(
    (details) => {{
        if (PROXY_CONFIG.username && PROXY_CONFIG.password) {{
            console.log('Providing proxy authentication');
            return {{
                authCredentials: {{
                    username: PROXY_CONFIG.username,
                    password: PROXY_CONFIG.password
                }}
            }};
        }}
        return {{}};
    }},
    {{urls: ["<all_urls>"]}},
    ["blocking"]
);

// Handle proxy errors
chrome.proxy.onProxyError.addListener((details) => {{
    console.error('Proxy error:', details);
    chrome.storage.local.set({{
        proxyStatus: 'error',
        lastError: details.error,
        errorTime: new Date().toISOString()
    }});
}});

// Message handling for popup communication
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {{
    if (request.action === 'getProxyStatus') {{
        chrome.storage.local.get(['proxyStatus', 'proxyHost', 'proxyPort', 'lastConfigured', 'lastError'], (result) => {{
            sendResponse(result);
        }});
        return true; // Indicates we will send a response asynchronously
    }}
    
    if (request.action === 'reconfigureProxy') {{
        configureProxy();
        sendResponse({{success: true}});
    }}
}});

// Initialize proxy on script load
configureProxy();
'''.strip()
    
    def _create_popup_html(self) -> str:
        """Create popup.html for the proxy extension"""
        return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 320px;
            padding: 10px;
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .section {
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
        }
        .section h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .status {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 3px;
        }
        .active { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .toggle-button {
            background-color: #28a745;
        }
        .toggle-button.disabled {
            background-color: #dc3545;
        }
        .toggle-button:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <h3>🌐 Proxy Extension</h3>

    <div class="section">
        <h4>Proxy Status</h4>
        <div id="proxy-status" class="status info">Loading...</div>
        <div id="proxy-details"></div>
        <button id="refresh-proxy">Refresh</button>
        <button id="test-proxy">Test Connection</button>
    </div>

    <div class="section">
        <h4>Connection Test</h4>
        <div id="test-results" class="status info">Click "Test Connection" to verify proxy</div>
        <div id="ip-info"></div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
'''.strip()
    
    def _create_popup_script(self) -> str:
        """Create popup.js for the proxy extension"""
        return '''
// Popup script for proxy extension
document.addEventListener('DOMContentLoaded', function() {
    loadProxyStatus();

    document.getElementById('refresh-proxy').addEventListener('click', loadProxyStatus);
    document.getElementById('test-proxy').addEventListener('click', testProxyConnection);
});

function loadProxyStatus() {
    chrome.runtime.sendMessage({action: 'getProxyStatus'}, function(response) {
        const statusDiv = document.getElementById('proxy-status');
        const detailsDiv = document.getElementById('proxy-details');

        if (response && response.configured) {
            statusDiv.className = 'status active';
            statusDiv.textContent = 'Proxy Active';
            if (response.config) {
                detailsDiv.innerHTML = `
                    <strong>Host:</strong> ${response.config.host}<br>
                    <strong>Port:</strong> ${response.config.port}<br>
                    <strong>Protocol:</strong> ${response.config.protocol}<br>
                    <strong>Username:</strong> ${response.config.username}<br>
                    <strong>Auth:</strong> ${response.config.hasCredentials ? '✅ Configured' : '❌ Missing'}
                `;
            }
        } else {
            statusDiv.className = 'status warning';
            statusDiv.textContent = 'Proxy Not Configured';
            detailsDiv.innerHTML = 'No proxy configuration found';
        }
    });
}

function testProxyConnection() {
    const testButton = document.getElementById('test-proxy');
    const testResults = document.getElementById('test-results');
    const ipInfo = document.getElementById('ip-info');

    testButton.disabled = true;
    testButton.textContent = 'Testing...';
    testResults.textContent = 'Testing proxy connection...';
    testResults.className = 'status info';

    chrome.runtime.sendMessage({action: 'testProxy'}, function(response) {
        testButton.disabled = false;
        testButton.textContent = 'Test Connection';

        if (response && response.success) {
            testResults.textContent = 'Connection successful!';
            testResults.className = 'status active';
            ipInfo.innerHTML = `
                <strong>Current IP:</strong> ${response.ip}<br>
                <strong>Test Time:</strong> ${new Date(response.timestamp).toLocaleTimeString()}
            `;
        } else {
            testResults.textContent = 'Connection failed';
            testResults.className = 'status error';
            ipInfo.innerHTML = `
                <strong>Error:</strong> ${response ? response.error : 'Unknown error'}<br>
                <strong>Test Time:</strong> ${new Date().toLocaleTimeString()}
            `;
        }
    });
}


'''.strip()
    
    def get_extension_path(self, extension_name: str = "proxy_extension") -> Optional[str]:
        """Get the path to an existing extension"""
        extension_path = os.path.join(self.extensions_dir, extension_name)
        if os.path.exists(extension_path):
            return extension_path
        return None
    
    def cleanup_extensions(self):
        """Clean up old extension files"""
        try:
            if os.path.exists(self.extensions_dir):
                import shutil
                shutil.rmtree(self.extensions_dir)
                os.makedirs(self.extensions_dir, exist_ok=True)
                self.logger.info("Cleaned up extension directory")
        except Exception as e:
            self.logger.error(f"Error cleaning up extensions: {str(e)}")

    def configure_proxy_for_profile(self, proxy_config: Dict, profile_identifier: str) -> bool:
        """Configure proxy settings for a specific profile using the shared extension"""
        try:
            # Store proxy configuration in a profile-specific storage
            config_dir = os.path.join(self.extensions_dir, 'configs')
            os.makedirs(config_dir, exist_ok=True)

            config_file = os.path.join(config_dir, f"{profile_identifier}_proxy_config.json")

            with open(config_file, 'w') as f:
                json.dump(proxy_config, f, indent=2)

            # Also update the shared extension's background.js with the current proxy config
            self._update_shared_extension_config(proxy_config)

            self.logger.info(f"Stored proxy configuration for profile {profile_identifier}")
            return True

        except Exception as e:
            self.logger.error(f"Error storing proxy configuration for profile {profile_identifier}: {str(e)}")
            return False

    def _update_shared_extension_config(self, proxy_config: Dict):
        """Update the shared extension's background.js with current proxy configuration"""
        try:
            if not self.shared_extension_path or not os.path.exists(self.shared_extension_path):
                self.logger.warning("Shared extension path not available for config update")
                return

            background_js_path = os.path.join(self.shared_extension_path, 'background.js')

            # Parse proxy URL to get components
            proxy_url = proxy_config.get('proxy', '')
            if not proxy_url:
                self.logger.warning("No proxy URL in configuration for extension update")
                return

            # Parse the proxy URL
            from urllib.parse import urlparse
            parsed = urlparse(proxy_url)

            if not parsed.hostname or not parsed.port:
                self.logger.warning("Invalid proxy URL format for extension update")
                return

            # Extract username and password from URL
            username = parsed.username or ''
            password = parsed.password or ''

            # Update the background.js file with current proxy settings
            background_js_content = f'''// Background script for proxy management
// CONFIGURE YOUR PROXY SETTINGS HERE
const PROXY_CONFIG = {{
  scheme: "{parsed.scheme or 'http'}",        // http, https, socks4, or socks5
  host: "{parsed.hostname}",    // Your proxy host
  port: {parsed.port},           // Your proxy port
  username: "{username}",    // Your proxy username
  password: "{password}"     // Your proxy password
}};

let proxyConfig = PROXY_CONFIG;
let isProxyEnabled = true;

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {{
  console.log('Proxy extension installed, enabling proxy...');
  setProxy(proxyConfig);
}});

chrome.runtime.onStartup.addListener(() => {{
  console.log('Browser started, enabling proxy...');
  setProxy(proxyConfig);
}});

// Set proxy configuration
async function setProxy(config) {{
  try {{
    const proxySettings = {{
      mode: "fixed_servers",
      rules: {{
        singleProxy: {{
          scheme: config.scheme || "http",
          host: config.host,
          port: parseInt(config.port)
        }}
      }}
    }};

    await chrome.proxy.settings.set({{
      value: proxySettings,
      scope: 'regular'
    }});

    console.log('Proxy set successfully:', proxySettings);
  }} catch (error) {{
    console.error('Error setting proxy:', error);
    throw error;
  }}
}}

// Clear proxy settings
async function clearProxy() {{
  try {{
    await chrome.proxy.settings.clear({{
      scope: 'regular'
    }});
    console.log('Proxy cleared successfully');
  }} catch (error) {{
    console.error('Error clearing proxy:', error);
    throw error;
  }}
}}

// Handle proxy authentication
chrome.webRequest.onAuthRequired.addListener(
  function(details) {{
    console.log('Proxy authentication required for:', details.url);
    return {{
      authCredentials: {{
        username: proxyConfig.username,
        password: proxyConfig.password
      }}
    }};
  }},
  {{urls: ["<all_urls>"]}},
  ["blocking"]
);

// Listen for configuration updates from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {{
  if (request.action === 'configureProxy') {{
    console.log('Received proxy configuration update:', request.proxyConfig);
    proxyConfig = request.proxyConfig;
    setProxy(proxyConfig).then(() => {{
      sendResponse({{success: true}});
    }}).catch((error) => {{
      console.error('Failed to configure proxy:', error);
      sendResponse({{success: false, error: error.message}});
    }});
    return true; // Will respond asynchronously
  }}
}});

// Handle proxy errors
chrome.proxy.onProxyError.addListener((details) => {{
  console.error('Proxy error:', details);
}});
'''

            with open(background_js_path, 'w', encoding='utf-8') as f:
                f.write(background_js_content)

            self.logger.info("Updated shared extension background.js with current proxy configuration")

        except Exception as e:
            self.logger.error(f"Error updating shared extension config: {str(e)}")

    def get_proxy_config_for_profile(self, profile_identifier: str) -> Optional[Dict]:
        """Get proxy configuration for a specific profile"""
        try:
            config_dir = os.path.join(self.extensions_dir, 'configs')
            config_file = os.path.join(config_dir, f"{profile_identifier}_proxy_config.json")

            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    return json.load(f)

            return None

        except Exception as e:
            self.logger.error(f"Error loading proxy configuration for profile {profile_identifier}: {str(e)}")
            return None
