// CSP Disabler Extension Background Script
console.log('CSP Disabler extension background script loaded');

// CSP disabling state
let isCSPDisabled = true; // Default to disabled for automation
let isInitializingCSP = true;
let ignoreNextStorageChange = false;

// Initialize CSP settings on extension startup
chrome.runtime.onStartup.addListener(() => {
    console.log('CSP Extension startup - initializing CSP settings');
    initializeCSPSettings();
});

chrome.runtime.onInstalled.addListener(() => {
    console.log('CSP Extension installed - initializing CSP settings');
    initializeCSPSettings();
});

// Initialize CSP settings
async function initializeCSPSettings() {
    try {
        const result = await chrome.storage.sync.get(['disableCSP']);
        isCSPDisabled = result.disableCSP !== undefined ? result.disableCSP : true;
        
        console.log('CSP disabled state:', isCSPDisabled);
        
        await updateCSPRule();
        updateExtensionIcon();
        isInitializingCSP = false;
        
        console.log('CSP settings initialized successfully');
    } catch (error) {
        console.error('Error initializing CSP settings:', error);
    }
}

// Update CSP rule based on current state
async function updateCSPRule() {
    try {
        // Remove existing rules first
        const existingRules = await chrome.declarativeNetRequest.getDynamicRules();
        const ruleIds = existingRules.map(rule => rule.id);
        
        if (ruleIds.length > 0) {
            await chrome.declarativeNetRequest.updateDynamicRules({
                removeRuleIds: ruleIds
            });
        }

        if (isCSPDisabled) {
            // Add rules to remove CSP headers
            const rules = [
                {
                    id: 1,
                    priority: 1,
                    action: {
                        type: "modifyHeaders",
                        responseHeaders: [
                            { header: "content-security-policy", operation: "remove" },
                            { header: "content-security-policy-report-only", operation: "remove" }
                        ]
                    },
                    condition: {
                        urlFilter: "*",
                        resourceTypes: ["main_frame", "sub_frame"]
                    }
                },
                {
                    id: 2,
                    priority: 1,
                    action: {
                        type: "modifyHeaders",
                        responseHeaders: [
                            { header: "x-frame-options", operation: "remove" },
                            { header: "x-content-type-options", operation: "remove" }
                        ]
                    },
                    condition: {
                        urlFilter: "*",
                        resourceTypes: ["main_frame", "sub_frame"]
                    }
                }
            ];

            await chrome.declarativeNetRequest.updateDynamicRules({
                addRules: rules
            });

            console.log('CSP disabling rules added');
        } else {
            console.log('CSP disabling rules removed');
        }

        // Update storage
        await chrome.storage.sync.set({ disableCSP: isCSPDisabled });
        
    } catch (error) {
        console.error('Error updating CSP rule:', error);
    }
}

// Update extension icon based on CSP state
function updateExtensionIcon() {
    const iconPath = isCSPDisabled ? 'icon-active.png' : 'icon-inactive.png';
    const title = isCSPDisabled ? 'CSP Disabled' : 'CSP Enabled';
    
    try {
        chrome.action.setTitle({ title: title });
        // Note: We'll use a simple colored badge instead of icon files
        chrome.action.setBadgeText({ text: isCSPDisabled ? 'OFF' : 'ON' });
        chrome.action.setBadgeBackgroundColor({ 
            color: isCSPDisabled ? '#ff4444' : '#44ff44' 
        });
    } catch (error) {
        console.error('Error updating extension icon:', error);
    }
}

// Listen for storage changes
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'sync' && changes.disableCSP && !isInitializingCSP && !ignoreNextStorageChange) {
        console.log('CSP setting changed:', changes.disableCSP.newValue);
        isCSPDisabled = changes.disableCSP.newValue;
        updateCSPRule().then(() => {
            updateExtensionIcon();
        });
    }
});

// Handle messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'toggleCSP') {
        console.log('Toggling CSP state from:', isCSPDisabled);
        isCSPDisabled = !isCSPDisabled;
        
        ignoreNextStorageChange = true;
        updateCSPRule().then(() => {
            updateExtensionIcon();
            ignoreNextStorageChange = false;
            sendResponse({ success: true, disabled: isCSPDisabled });
        }).catch(error => {
            console.error('Error toggling CSP:', error);
            ignoreNextStorageChange = false;
            sendResponse({ success: false, error: error.message });
        });
        
        return true; // Keep message channel open for async response
    } else if (request.action === 'getCSPState') {
        sendResponse({ disabled: isCSPDisabled });
    }
});

// External API for automation scripts
chrome.runtime.onMessageExternal.addListener((request, sender, sendResponse) => {
    if (request.action === 'setCSPState') {
        console.log('External CSP state change request:', request.disabled);
        isCSPDisabled = request.disabled;
        
        updateCSPRule().then(() => {
            updateExtensionIcon();
            sendResponse({ success: true, disabled: isCSPDisabled });
        }).catch(error => {
            console.error('Error setting CSP state externally:', error);
            sendResponse({ success: false, error: error.message });
        });
        
        return true; // Keep message channel open for async response
    } else if (request.action === 'getCSPState') {
        sendResponse({ disabled: isCSPDisabled });
    }
});

// Initialize on script load
initializeCSPSettings();
