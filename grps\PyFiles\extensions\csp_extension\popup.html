<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .status-indicator {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .status-text {
            font-size: 14px;
            font-weight: bold;
        }
        
        .controls {
            text-align: center;
        }
        
        .toggle-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            width: 100%;
            box-sizing: border-box;
        }
        
        .toggle-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .info {
            margin-top: 15px;
            font-size: 12px;
            text-align: center;
            opacity: 0.8;
        }
        
        .disabled {
            color: #ff6b6b;
        }
        
        .enabled {
            color: #51cf66;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ CSP Disabler</h1>
    </div>
    
    <div class="status">
        <div class="status-indicator" id="statusIndicator">🔄</div>
        <div class="status-text" id="statusText">Loading...</div>
    </div>
    
    <div class="controls">
        <button class="toggle-btn" id="toggleBtn">Toggle CSP</button>
    </div>
    
    <div class="info">
        <p>Content Security Policy (CSP) controls browser security features. Disabling CSP allows automation tools to work properly.</p>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
